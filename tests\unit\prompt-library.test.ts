/**
 * Unit tests for Prompt Library module
 */

import { PromptLibrary } from '../../src/services/prompt-library/PromptLibrary';
import { PromptTemplate, PromptCategory } from '../../src/services/prompt-library/types';

describe('PromptLibrary', () => {
  let promptLibrary: PromptLibrary;

  beforeEach(async () => {
    promptLibrary = new PromptLibrary();
    await promptLibrary.initialize();
  });

  afterEach(async () => {
    await promptLibrary.cleanup();
  });

  describe('Prompt Management', () => {
    test('should create a new prompt', async () => {
      const promptData = {
        title: 'Test Prompt',
        content: 'This is a test prompt with {{variable}}',
        description: 'A test prompt for unit testing',
        category: 'Testing',
        tags: ['test', 'unit'],
        variables: [{ name: 'variable', description: 'Test variable', required: true }]
      };

      const prompt = await promptLibrary.createPrompt(promptData);

      expect(prompt).toBeDefined();
      expect(prompt.id).toBeDefined();
      expect(prompt.title).toBe(promptData.title);
      expect(prompt.content).toBe(promptData.content);
      expect(prompt.category).toBe(promptData.category);
      expect(prompt.tags).toEqual(promptData.tags);
      expect(prompt.variables).toEqual(promptData.variables);
    });

    test('should retrieve a prompt by ID', async () => {
      const promptData = {
        title: 'Retrievable Prompt',
        content: 'Content for retrieval test',
        category: 'Testing'
      };

      const createdPrompt = await promptLibrary.createPrompt(promptData);
      const retrievedPrompt = await promptLibrary.getPrompt(createdPrompt.id);

      expect(retrievedPrompt).toBeDefined();
      expect(retrievedPrompt?.id).toBe(createdPrompt.id);
      expect(retrievedPrompt?.title).toBe(promptData.title);
    });

    test('should update an existing prompt', async () => {
      const promptData = {
        title: 'Original Title',
        content: 'Original content',
        category: 'Testing'
      };

      const createdPrompt = await promptLibrary.createPrompt(promptData);
      
      const updateData = {
        title: 'Updated Title',
        content: 'Updated content',
        description: 'Added description'
      };

      const updatedPrompt = await promptLibrary.updatePrompt(createdPrompt.id, updateData);

      expect(updatedPrompt.title).toBe(updateData.title);
      expect(updatedPrompt.content).toBe(updateData.content);
      expect(updatedPrompt.description).toBe(updateData.description);
      expect(updatedPrompt.category).toBe(promptData.category); // Should remain unchanged
    });

    test('should delete a prompt', async () => {
      const promptData = {
        title: 'Deletable Prompt',
        content: 'This prompt will be deleted',
        category: 'Testing'
      };

      const createdPrompt = await promptLibrary.createPrompt(promptData);
      
      await expect(promptLibrary.deletePrompt(createdPrompt.id)).resolves.toBe(true);
      
      const retrievedPrompt = await promptLibrary.getPrompt(createdPrompt.id);
      expect(retrievedPrompt).toBeNull();
    });

    test('should list all prompts', async () => {
      const prompt1 = await promptLibrary.createPrompt({
        title: 'Prompt 1',
        content: 'Content 1',
        category: 'Testing'
      });

      const prompt2 = await promptLibrary.createPrompt({
        title: 'Prompt 2',
        content: 'Content 2',
        category: 'Testing'
      });

      const prompts = await promptLibrary.getAllPrompts();

      expect(prompts.length).toBeGreaterThanOrEqual(2);
      expect(prompts.some(p => p.id === prompt1.id)).toBe(true);
      expect(prompts.some(p => p.id === prompt2.id)).toBe(true);
    });
  });

  describe('Search and Filtering', () => {
    beforeEach(async () => {
      // Create test prompts
      await promptLibrary.createPrompt({
        title: 'JavaScript Helper',
        content: 'Help with JavaScript code',
        category: 'Programming',
        tags: ['javascript', 'coding', 'help']
      });

      await promptLibrary.createPrompt({
        title: 'Python Assistant',
        content: 'Assist with Python development',
        category: 'Programming',
        tags: ['python', 'coding', 'development']
      });

      await promptLibrary.createPrompt({
        title: 'Writing Helper',
        content: 'Help with creative writing',
        category: 'Writing',
        tags: ['writing', 'creative', 'help']
      });
    });

    test('should search prompts by title', async () => {
      const results = await promptLibrary.searchPrompts('JavaScript');

      expect(results.length).toBe(1);
      expect(results[0].title).toBe('JavaScript Helper');
    });

    test('should search prompts by content', async () => {
      const results = await promptLibrary.searchPrompts('Python development');

      expect(results.length).toBe(1);
      expect(results[0].title).toBe('Python Assistant');
    });

    test('should search prompts by tags', async () => {
      const results = await promptLibrary.searchPrompts('coding');

      expect(results.length).toBe(2);
      expect(results.some(p => p.title === 'JavaScript Helper')).toBe(true);
      expect(results.some(p => p.title === 'Python Assistant')).toBe(true);
    });

    test('should filter prompts by category', async () => {
      const results = await promptLibrary.getPromptsByCategory('Programming');

      expect(results.length).toBe(2);
      expect(results.every(p => p.category === 'Programming')).toBe(true);
    });

    test('should filter prompts by tags', async () => {
      const results = await promptLibrary.getPromptsByTags(['help']);

      expect(results.length).toBe(2);
      expect(results.some(p => p.title === 'JavaScript Helper')).toBe(true);
      expect(results.some(p => p.title === 'Writing Helper')).toBe(true);
    });
  });

  describe('Categories Management', () => {
    test('should create a new category', async () => {
      const categoryData = {
        name: 'Test Category',
        description: 'A category for testing',
        color: '#ff0000'
      };

      const category = await promptLibrary.createCategory(categoryData);

      expect(category).toBeDefined();
      expect(category.id).toBeDefined();
      expect(category.name).toBe(categoryData.name);
      expect(category.description).toBe(categoryData.description);
      expect(category.color).toBe(categoryData.color);
    });

    test('should list all categories', async () => {
      await promptLibrary.createCategory({
        name: 'Category 1',
        description: 'First category'
      });

      await promptLibrary.createCategory({
        name: 'Category 2',
        description: 'Second category'
      });

      const categories = await promptLibrary.getAllCategories();

      expect(categories.length).toBeGreaterThanOrEqual(2);
      expect(categories.some(c => c.name === 'Category 1')).toBe(true);
      expect(categories.some(c => c.name === 'Category 2')).toBe(true);
    });

    test('should update a category', async () => {
      const category = await promptLibrary.createCategory({
        name: 'Original Category',
        description: 'Original description'
      });

      const updateData = {
        name: 'Updated Category',
        description: 'Updated description',
        color: '#00ff00'
      };

      const updatedCategory = await promptLibrary.updateCategory(category.id, updateData);

      expect(updatedCategory.name).toBe(updateData.name);
      expect(updatedCategory.description).toBe(updateData.description);
      expect(updatedCategory.color).toBe(updateData.color);
    });

    test('should delete a category', async () => {
      const category = await promptLibrary.createCategory({
        name: 'Deletable Category',
        description: 'This category will be deleted'
      });

      await expect(promptLibrary.deleteCategory(category.id)).resolves.toBe(true);

      const categories = await promptLibrary.getAllCategories();
      expect(categories.some(c => c.id === category.id)).toBe(false);
    });
  });

  describe('Template Processing', () => {
    test('should process template variables', async () => {
      const prompt = await promptLibrary.createPrompt({
        title: 'Template Test',
        content: 'Hello {{name}}, you are {{age}} years old.',
        category: 'Testing',
        variables: [
          { name: 'name', description: 'Person name', required: true },
          { name: 'age', description: 'Person age', required: true }
        ]
      });

      const processed = await promptLibrary.processTemplate(prompt.id, {
        name: 'John',
        age: '25'
      });

      expect(processed).toBe('Hello John, you are 25 years old.');
    });

    test('should handle missing required variables', async () => {
      const prompt = await promptLibrary.createPrompt({
        title: 'Required Variables Test',
        content: 'Hello {{name}}, welcome!',
        category: 'Testing',
        variables: [
          { name: 'name', description: 'Person name', required: true }
        ]
      });

      await expect(promptLibrary.processTemplate(prompt.id, {}))
        .rejects.toThrow('Missing required variable: name');
    });

    test('should use default values for optional variables', async () => {
      const prompt = await promptLibrary.createPrompt({
        title: 'Default Values Test',
        content: 'Hello {{name}}, your role is {{role}}.',
        category: 'Testing',
        variables: [
          { name: 'name', description: 'Person name', required: true },
          { name: 'role', description: 'Person role', required: false, defaultValue: 'user' }
        ]
      });

      const processed = await promptLibrary.processTemplate(prompt.id, {
        name: 'Alice'
      });

      expect(processed).toBe('Hello Alice, your role is user.');
    });
  });

  describe('Import/Export', () => {
    test('should export prompts', async () => {
      await promptLibrary.createPrompt({
        title: 'Export Test',
        content: 'This prompt will be exported',
        category: 'Testing'
      });

      const exportData = await promptLibrary.exportPrompts();

      expect(exportData).toBeDefined();
      expect(exportData.prompts).toBeDefined();
      expect(exportData.prompts.length).toBeGreaterThan(0);
      expect(exportData.version).toBeDefined();
      expect(exportData.exportedAt).toBeDefined();
    });

    test('should import prompts', async () => {
      const importData = {
        version: '1.0.0',
        exportedAt: new Date(),
        prompts: [
          {
            title: 'Imported Prompt',
            content: 'This prompt was imported',
            category: 'Imported',
            tags: ['import', 'test']
          }
        ],
        categories: [
          {
            name: 'Imported',
            description: 'Imported category'
          }
        ]
      };

      const result = await promptLibrary.importPrompts(importData);

      expect(result.imported).toBe(1);
      expect(result.errors).toEqual([]);

      const prompts = await promptLibrary.searchPrompts('Imported Prompt');
      expect(prompts.length).toBe(1);
      expect(prompts[0].title).toBe('Imported Prompt');
    });
  });

  describe('Error Handling', () => {
    test('should handle non-existent prompt retrieval', async () => {
      const prompt = await promptLibrary.getPrompt('non-existent-id');
      expect(prompt).toBeNull();
    });

    test('should handle invalid prompt creation', async () => {
      await expect(promptLibrary.createPrompt({
        title: '', // Invalid: empty title
        content: 'Some content',
        category: 'Testing'
      })).rejects.toThrow();
    });

    test('should handle duplicate category names', async () => {
      await promptLibrary.createCategory({
        name: 'Duplicate Category',
        description: 'First category'
      });

      await expect(promptLibrary.createCategory({
        name: 'Duplicate Category',
        description: 'Second category'
      })).rejects.toThrow();
    });
  });
});
