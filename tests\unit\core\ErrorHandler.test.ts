/**
 * Error handling module unit tests
 */

import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, get<PERSON><PERSON>r<PERSON><PERSON><PERSON>, initialize<PERSON>rror<PERSON><PERSON><PERSON> } from '../../../src/core/error/ErrorHandler';
import { 
  ApplicationError, 
  ValidationError, 
  NetworkError, 
  ErrorClassifier 
} from '../../../src/core/error/ErrorTypes';
import { ConsoleErrorReporter, ErrorAnalyticsTracker } from '../../../src/core/error/ErrorReporter';
import { ErrorCodes } from '../../../src/core/error/types';

// Mock console methods
const originalConsole = { ...console };
beforeAll(() => {
  console.error = jest.fn();
  console.warn = jest.fn();
  console.info = jest.fn();
  console.group = jest.fn();
  console.groupEnd = jest.fn();
});

afterAll(() => {
  Object.assign(console, originalConsole);
});

describe('ErrorHand<PERSON>', () => {
  let errorHandler: <PERSON><PERSON>r<PERSON><PERSON>ler;

  beforeEach(() => {
    errorHandler = new ErrorHandler({
      enableReporting: true,
      enableRecovery: true,
      enableUserNotification: false, // Disable for tests
      enableAnalytics: true
    });
  });

  afterEach(() => {
    errorHandler.removeAllListeners();
  });

  describe('Error Handling', () => {
    test('should handle basic Error instances', async () => {
      const error = new Error('Test error');
      const handlePromise = new Promise(resolve => {
        errorHandler.once('error-handled', resolve);
      });

      await errorHandler.handle(error, { component: 'test', operation: 'test-op' });
      
      const handledError = await handlePromise;
      expect(handledError).toBeDefined();
    });

    test('should handle ApplicationError instances', async () => {
      const appError = new ApplicationError(
        {
          code: ErrorCodes.VALIDATION_FAILED,
          message: 'Test validation error',
          category: 'validation',
          severity: 'medium'
        },
        { component: 'test', operation: 'validation' }
      );

      const handlePromise = new Promise(resolve => {
        errorHandler.once('error-handled', resolve);
      });

      await errorHandler.handle(appError);
      
      const handledError = await handlePromise;
      expect(handledError).toBeDefined();
    });

    test('should create AppError from regular Error', async () => {
      const error = new Error('Network timeout');
      const handlePromise = new Promise(resolve => {
        errorHandler.once('error-handled', resolve);
      });

      await errorHandler.handle(error, { component: 'network', operation: 'fetch' });

      const handledError = await handlePromise;
      expect(handledError).toBeDefined();
    });

    test('should emit error-handled event', async () => {
      const error = new Error('Test error');
      const eventPromise = new Promise(resolve => {
        errorHandler.once('error-handled', resolve);
      });

      await errorHandler.handle(error);
      
      const handledError = await eventPromise;
      expect(handledError).toBeDefined();
    });
  });

  describe('Error Creation', () => {
    test('should create ApplicationError with details', () => {
      const error = errorHandler.createError(
        {
          code: ErrorCodes.NETWORK_ERROR,
          message: 'Connection failed',
          category: 'network',
          severity: 'high'
        },
        { component: 'api', operation: 'request' }
      );

      expect(error).toBeInstanceOf(ApplicationError);
      expect(error.code).toBe(ErrorCodes.NETWORK_ERROR);
      expect(error.category).toBe('network');
      expect(error.severity).toBe('high');
      expect(error.context.component).toBe('api');
    });

    test('should generate unique error IDs', () => {
      const error1 = errorHandler.createError({ message: 'Error 1' });
      const error2 = errorHandler.createError({ message: 'Error 2' });

      expect(error1.id).not.toBe(error2.id);
      expect(error1.id).toMatch(/^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/);
    });
  });

  describe('Configuration', () => {
    test('should configure error handler', () => {
      const config = {
        enableReporting: false,
        maxRetries: 5,
        retryDelay: 2000
      };

      errorHandler.configure(config);
      
      // Configuration should be applied (we can't directly test private config)
      expect(() => errorHandler.configure(config)).not.toThrow();
    });

    test('should set error reporter', () => {
      const reporter = new ConsoleErrorReporter();
      
      expect(() => errorHandler.setReporter(reporter)).not.toThrow();
    });
  });

  describe('Recovery Handlers', () => {
    test('should register recovery handler', () => {
      const mockHandler = {
        canRecover: jest.fn().mockReturnValue(true),
        recover: jest.fn().mockResolvedValue(true)
      };

      errorHandler.registerRecoveryHandler('network', mockHandler);
      
      // Should not throw
      expect(() => errorHandler.registerRecoveryHandler('network', mockHandler)).not.toThrow();
    });
  });

  describe('Analytics', () => {
    test('should get analytics', async () => {
      const analytics = await errorHandler.getAnalytics();
      
      expect(Array.isArray(analytics)).toBe(true);
    });

    test('should get analytics for time range', async () => {
      const timeRange = {
        start: new Date(Date.now() - 24 * 60 * 60 * 1000), // 24 hours ago
        end: new Date()
      };
      
      const analytics = await errorHandler.getAnalytics(timeRange);
      
      expect(Array.isArray(analytics)).toBe(true);
    });
  });
});

describe('ApplicationError', () => {
  test('should create with all properties', () => {
    const error = new ApplicationError(
      {
        code: ErrorCodes.VALIDATION_FAILED,
        message: 'Validation failed',
        category: 'validation',
        severity: 'medium',
        recoverable: true,
        userMessage: 'Please check your input',
        suggestions: ['Check required fields']
      },
      {
        component: 'form',
        operation: 'submit',
        userId: 'user123'
      }
    );

    expect(error.code).toBe(ErrorCodes.VALIDATION_FAILED);
    expect(error.message).toBe('Validation failed');
    expect(error.category).toBe('validation');
    expect(error.severity).toBe('medium');
    expect(error.recoverable).toBe(true);
    expect(error.context.component).toBe('form');
    expect(error.context.userId).toBe('user123');
    expect(error.id).toBeDefined();
    expect(error.timestamp).toBeInstanceOf(Date);
  });

  test('should serialize to JSON', () => {
    const error = new ApplicationError(
      { message: 'Test error', category: 'test', severity: 'low' },
      { component: 'test', operation: 'test' }
    );

    const json = error.toJSON();
    
    expect(json.id).toBe(error.id);
    expect(json.message).toBe('Test error');
    expect(json.category).toBe('test');
    expect(json.timestamp).toBeDefined();
  });

  test('should create from JSON', () => {
    const originalError = new ApplicationError(
      { message: 'Test error', category: 'test', severity: 'low' },
      { component: 'test', operation: 'test' }
    );

    const json = originalError.toJSON();
    const restoredError = ApplicationError.fromJSON(json);
    
    expect(restoredError.message).toBe(originalError.message);
    expect(restoredError.category).toBe(originalError.category);
    expect(restoredError.context.component).toBe(originalError.context.component);
  });
});

describe('Specific Error Types', () => {
  test('should create ValidationError', () => {
    const error = new ValidationError('Required field missing', 'email', '', { component: 'form' });
    
    expect(error).toBeInstanceOf(ApplicationError);
    expect(error.category).toBe('validation');
    expect(error.code).toBe(ErrorCodes.VALIDATION_FAILED);
    expect(error.context.metadata?.field).toBe('email');
  });

  test('should create NetworkError', () => {
    const error = new NetworkError('Connection timeout', 408, '/api/data', { component: 'api' });
    
    expect(error).toBeInstanceOf(ApplicationError);
    expect(error.category).toBe('network');
    expect(error.code).toBe(ErrorCodes.NETWORK_ERROR);
    expect(error.context.metadata?.statusCode).toBe(408);
    expect(error.context.metadata?.endpoint).toBe('/api/data');
  });
});

describe('ErrorClassifier', () => {
  test('should classify network errors', () => {
    const error = new Error('fetch failed');
    const { category, severity } = ErrorClassifier.classify(error);
    
    expect(category).toBe('network');
    expect(severity).toBe('medium');
  });

  test('should classify file system errors', () => {
    const error = new Error('ENOENT: file not found');
    const { category, severity } = ErrorClassifier.classify(error);
    
    expect(category).toBe('filesystem');
    expect(severity).toBe('medium');
  });

  test('should classify validation errors', () => {
    const error = new Error('invalid input provided');
    const { category, severity } = ErrorClassifier.classify(error);
    
    expect(category).toBe('validation');
    expect(severity).toBe('low');
  });

  test('should classify unknown errors', () => {
    const error = new Error('some random error');
    const { category, severity } = ErrorClassifier.classify(error);
    
    expect(category).toBe('unknown');
    expect(severity).toBe('medium');
  });

  test('should determine if error is recoverable', () => {
    const recoverableError = new Error('network timeout');
    const nonRecoverableError = new Error('out of memory');
    
    expect(ErrorClassifier.isRecoverable(recoverableError)).toBe(true);
    // Most errors are considered recoverable by default
    expect(ErrorClassifier.isRecoverable(nonRecoverableError)).toBe(true);
  });

  test('should handle ApplicationError instances', () => {
    const appError = new ApplicationError(
      { category: 'database', severity: 'high', recoverable: false },
      { component: 'test', operation: 'test' }
    );
    
    const { category, severity } = ErrorClassifier.classify(appError);
    expect(category).toBe('database');
    expect(severity).toBe('high');
    
    expect(ErrorClassifier.isRecoverable(appError)).toBe(false);
  });
});

describe('ErrorAnalyticsTracker', () => {
  let tracker: ErrorAnalyticsTracker;

  beforeEach(() => {
    tracker = new ErrorAnalyticsTracker();
  });

  test('should track error occurrences', () => {
    const error = new ApplicationError(
      { code: ErrorCodes.NETWORK_ERROR, category: 'network', severity: 'medium' },
      { component: 'api', operation: 'fetch' }
    );

    tracker.track(error);
    
    const analytics = tracker.getAnalytics();
    expect(analytics).toHaveLength(1);
    expect(analytics[0].frequency).toBe(1);
  });

  test('should aggregate multiple occurrences', () => {
    const error1 = new ApplicationError(
      { code: ErrorCodes.NETWORK_ERROR, category: 'network', severity: 'medium' },
      { component: 'api', operation: 'fetch' }
    );
    const error2 = new ApplicationError(
      { code: ErrorCodes.NETWORK_ERROR, category: 'network', severity: 'medium' },
      { component: 'api', operation: 'fetch' }
    );

    tracker.track(error1);
    tracker.track(error2);
    
    const analytics = tracker.getAnalytics();
    expect(analytics).toHaveLength(1);
    expect(analytics[0].frequency).toBe(2);
  });

  test('should get top errors', () => {
    // Create multiple different errors
    for (let i = 0; i < 5; i++) {
      const error = new ApplicationError(
        { code: `ERROR_${i}`, category: 'test', severity: 'medium' },
        { component: 'test', operation: 'test' }
      );
      
      // Track error multiple times to create different frequencies
      for (let j = 0; j <= i; j++) {
        tracker.track(error);
      }
    }
    
    const topErrors = tracker.getTopErrors(3);
    expect(topErrors).toHaveLength(3);
    expect(topErrors[0].frequency).toBeGreaterThan(topErrors[1].frequency);
  });

  test('should clear analytics', () => {
    const error = new ApplicationError(
      { code: ErrorCodes.NETWORK_ERROR, category: 'network', severity: 'medium' },
      { component: 'api', operation: 'fetch' }
    );

    tracker.track(error);
    expect(tracker.getAnalytics()).toHaveLength(1);
    
    tracker.clear();
    expect(tracker.getAnalytics()).toHaveLength(0);
  });
});

describe('Global Error Handler', () => {
  test('should get global error handler instance', () => {
    const handler1 = getErrorHandler();
    const handler2 = getErrorHandler();
    
    expect(handler1).toBe(handler2); // Should be singleton
    expect(handler1).toBeInstanceOf(ErrorHandler);
  });

  test('should initialize new global error handler', () => {
    const config = { enableReporting: false };
    const handler = initializeErrorHandler(config);
    
    expect(handler).toBeInstanceOf(ErrorHandler);
    expect(getErrorHandler()).toBe(handler);
  });
});
