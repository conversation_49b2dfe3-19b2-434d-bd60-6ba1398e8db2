/**
 * Logger module unit tests
 */

import { Logger } from '../../../src/core/logger/Logger';
import { ConsoleTransport } from '../../../src/core/logger/ConsoleTransport';
import { PerformanceLogger } from '../../../src/core/logger/PerformanceLogger';
import { LoggerFactory } from '../../../src/core/logger/LoggerFactory';
import { LevelFilter } from '../../../src/core/logger/filters';
import { LogLevel, LogTransport, LogEntry } from '../../../src/core/logger/types';

// Mock transport for testing
class MockTransport implements LogTransport {
  public entries: LogEntry[] = [];

  async write(entry: LogEntry): Promise<void> {
    this.entries.push(entry);
  }

  clear(): void {
    this.entries = [];
  }
}

describe('Logger', () => {
  let logger: Logger;
  let mockTransport: MockTransport;

  beforeEach(() => {
    mockTransport = new MockTransport();
    logger = new Logger();
    logger.addTransport(mockTransport);
  });

  afterEach(() => {
    mockTransport.clear();
  });

  describe('Basic Logging', () => {
    test('should log info message', async () => {
      await logger.info('Test message', { tag: 'test' });

      expect(mockTransport.entries).toHaveLength(1);
      expect(mockTransport.entries[0]).toMatchObject({
        level: LogLevel.INFO,
        message: 'Test message',
        metadata: { tag: 'test' }
      });
    });

    test('should log error with Error object', async () => {
      const error = new Error('Test error');
      await logger.error(error);

      expect(mockTransport.entries).toHaveLength(1);
      expect(mockTransport.entries[0]).toMatchObject({
        level: LogLevel.ERROR,
        message: 'Test error'
      });
      expect(mockTransport.entries[0].metadata?.error).toBeDefined();
    });

    test('should respect log level filtering', async () => {
      logger.setLevel(LogLevel.WARN);

      await logger.debug('Debug message');
      await logger.info('Info message');
      await logger.warn('Warning message');

      expect(mockTransport.entries).toHaveLength(1);
      expect(mockTransport.entries[0].level).toBe(LogLevel.WARN);
    });
  });

  describe('Context and Child Loggers', () => {
    test('should create child logger with additional context', async () => {
      logger.setContext({ module: 'test' });
      const childLogger = logger.child({ component: 'child' });
      childLogger.addTransport(mockTransport);

      await childLogger.info('Child message');

      expect(mockTransport.entries[0].context).toMatchObject({
        module: 'test',
        component: 'child'
      });
    });
  });

  describe('Filters', () => {
    test('should apply level filter', async () => {
      const levelFilter = new LevelFilter(LogLevel.WARN);
      logger.addFilter(levelFilter);

      await logger.debug('Debug message');
      await logger.info('Info message');
      await logger.warn('Warning message');

      expect(mockTransport.entries).toHaveLength(1);
      expect(mockTransport.entries[0].level).toBe(LogLevel.WARN);
    });
  });

  describe('Performance Timing', () => {
    test('should measure performance', () => {
      const perfLogger = new PerformanceLogger(logger);

      const result = perfLogger.measureSync('test-operation', () => {
        return 'result';
      });

      expect(result).toBe('result');
      expect(mockTransport.entries).toHaveLength(1);
      expect(mockTransport.entries[0].message).toContain('Performance: test-operation');
      expect(mockTransport.entries[0].metadata?.performance).toBeDefined();
    });

    test('should handle timer operations', () => {
      logger.time('test-timer');
      const duration = logger.timeEnd('test-timer');

      expect(duration).toBeGreaterThanOrEqual(0);
      expect(mockTransport.entries).toHaveLength(1);
      expect(mockTransport.entries[0].message).toContain('Performance: test-timer');
    });
  });
});

describe('LoggerFactory', () => {
  let factory: LoggerFactory;

  beforeEach(() => {
    factory = LoggerFactory.getInstance();
  });

  test('should create logger instances', () => {
    const logger1 = factory.getLogger('test1');
    const logger2 = factory.getLogger('test2');
    const logger1Again = factory.getLogger('test1');

    expect(logger1).toBeDefined();
    expect(logger2).toBeDefined();
    expect(logger1).toBe(logger1Again); // Should return same instance
    expect(logger1).not.toBe(logger2);
  });

  test('should create performance logger', () => {
    const perfLogger = factory.getPerformanceLogger('test');
    expect(perfLogger).toBeInstanceOf(PerformanceLogger);
  });

  test('should configure default settings', () => {
    const mockTransport = new MockTransport();
    
    factory.configure({
      level: LogLevel.DEBUG,
      transports: [mockTransport],
      filters: []
    });

    const logger = factory.getLogger('configured-test');
    // Logger should have the configured transport
    expect(logger).toBeDefined();
  });
});
