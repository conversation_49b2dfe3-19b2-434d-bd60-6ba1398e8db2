/**
 * Jest test setup file
 * This file is run before each test suite
 */

// Mock Electron APIs for testing
const mockElectron = {
  app: {
    getVersion: jest.fn(() => '1.0.0'),
    getPath: jest.fn(() => '/mock/path'),
    whenReady: jest.fn(() => Promise.resolve()),
    on: jest.fn(),
    quit: jest.fn(),
  },
  BrowserWindow: jest.fn(() => ({
    loadFile: jest.fn(),
    loadURL: jest.fn(),
    on: jest.fn(),
    once: jest.fn(),
    show: jest.fn(),
    hide: jest.fn(),
    close: jest.fn(),
    minimize: jest.fn(),
    maximize: jest.fn(),
    isMaximized: jest.fn(() => false),
    unmaximize: jest.fn(),
    webContents: {
      openDevTools: jest.fn(),
      on: jest.fn(),
    },
  })),
  ipcMain: {
    handle: jest.fn(),
    on: jest.fn(),
    removeAllListeners: jest.fn(),
  },
  ipcRenderer: {
    invoke: jest.fn(),
    send: jest.fn(),
    on: jest.fn(),
    off: jest.fn(),
    removeAllListeners: jest.fn(),
  },
  contextBridge: {
    exposeInMainWorld: jest.fn(),
  },
};

// Mock electron module
jest.mock('electron', () => mockElectron);

// Mock better-sqlite3 for database tests
jest.mock('better-sqlite3', () => {
  return jest.fn(() => ({
    prepare: jest.fn(() => ({
      run: jest.fn(),
      get: jest.fn(),
      all: jest.fn(),
    })),
    exec: jest.fn(),
    close: jest.fn(),
    transaction: jest.fn(),
  }));
});

// Global test utilities
global.mockElectron = mockElectron;

// Mock DOM APIs for Node.js environment
global.document = {
  createElement: jest.fn(() => ({
    style: {},
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    appendChild: jest.fn(),
    removeChild: jest.fn(),
    querySelector: jest.fn(),
    querySelectorAll: jest.fn(),
    innerHTML: '',
    focus: jest.fn(),
    blur: jest.fn()
  })),
  body: {
    appendChild: jest.fn(),
    removeChild: jest.fn(),
    style: {}
  },
  documentElement: {
    style: {
      setProperty: jest.fn(),
      getProperty: jest.fn()
    }
  }
} as any;

global.window = {
  matchMedia: jest.fn(() => ({
    matches: false,
    addListener: jest.fn(),
    removeListener: jest.fn()
  }))
} as any;

// Suppress console.log in tests unless explicitly needed
const originalConsoleLog = console.log;
console.log = (...args: any[]) => {
  if (process.env.VERBOSE_TESTS === 'true') {
    originalConsoleLog(...args);
  }
};

// Setup global test timeout
jest.setTimeout(30000);
