/**
 * Storage module unit tests
 */

import * as path from 'path';
import * as os from 'os';
import { SQLiteStorageManager } from '../../../src/storage/StorageManager';
import { DatabaseConfig } from '../../../src/storage/types';
import { Prompt } from '../../../src/types/models/Prompt';
import { Category } from '../../../src/types/models/Category';

// Mock file system
jest.mock('../../../src/core/filesystem', () => ({
  getFileSystemManager: () => ({
    exists: jest.fn().mockResolvedValue(true),
    createDirectory: jest.fn().mockResolvedValue(undefined),
    copyFile: jest.fn().mockResolvedValue(undefined),
    deleteFile: jest.fn().mockResolvedValue(undefined),
    listDirectory: jest.fn().mockResolvedValue([])
  })
}));

describe('SQLiteStorageManager', () => {
  let storageManager: SQLiteStorageManager;
  let testConfig: DatabaseConfig;

  beforeEach(async () => {
    // Use in-memory database for tests
    testConfig = {
      path: ':memory:',
      encryption: { enabled: false },
      performance: {
        journalMode: 'MEMORY',
        synchronous: 'OFF',
        cacheSize: 1000,
        mmapSize: 0
      },
      backup: {
        enabled: false,
        interval: 60,
        maxBackups: 5
      }
    };

    storageManager = new SQLiteStorageManager(testConfig);
    await storageManager.initialize();
  });

  afterEach(async () => {
    if (storageManager) {
      await storageManager.close();
    }
  });

  describe('Initialization', () => {
    test('should initialize successfully', async () => {
      expect(storageManager).toBeDefined();
      expect(storageManager.getConnection()).toBeDefined();
    });

    test('should run migrations on initialization', async () => {
      const stats = await storageManager.getStats();
      expect(stats.schemaVersion).toBeGreaterThan(0);
    });

    test('should create repositories', () => {
      expect(storageManager.prompts).toBeDefined();
      expect(storageManager.categories).toBeDefined();
      expect(storageManager.settings).toBeDefined();
    });
  });

  describe('Database Operations', () => {
    test('should execute transactions', async () => {
      const result = await storageManager.transaction(async (tx) => {
        return 'test result';
      });

      expect(result).toBe('test result');
    });

    test('should get database statistics', async () => {
      const stats = await storageManager.getStats();

      expect(stats).toHaveProperty('size');
      expect(stats).toHaveProperty('pageCount');
      expect(stats).toHaveProperty('pageSize');
      expect(stats).toHaveProperty('tables');
      expect(Array.isArray(stats.tables)).toBe(true);
    });

    test('should vacuum database', async () => {
      await expect(storageManager.vacuum()).resolves.not.toThrow();
    });
  });

  describe('Prompt Repository', () => {
    test('should save and retrieve prompt', async () => {
      const prompt: Prompt = {
        title: 'Test Prompt',
        content: 'Test content',
        categoryId: 'general',
        tags: ['test'],
        variables: [],
        metadata: {
          author: 'test',
          createdAt: new Date(),
          updatedAt: new Date(),
          source: 'user',
          language: 'en',
          estimatedTokens: 10
        },
        version: 1,
        isTemplate: false,
        isFavorite: false,
        isArchived: false,
        usage: { count: 0, lastUsed: new Date() },
        createdAt: new Date(),
        updatedAt: new Date(),
        createdBy: 'user'
      };

      const id = await storageManager.prompts.save(prompt);
      expect(id).toBeDefined();

      const retrieved = await storageManager.prompts.findById(id);
      expect(retrieved).toBeDefined();
      expect(retrieved?.title).toBe(prompt.title);
      expect(retrieved?.content).toBe(prompt.content);
    });

    test('should update prompt', async () => {
      const prompt: Prompt = {
        title: 'Original Title',
        content: 'Original content',
        categoryId: 'general',
        tags: [],
        variables: [],
        metadata: {
          author: 'test',
          createdAt: new Date(),
          updatedAt: new Date(),
          source: 'user',
          language: 'en',
          estimatedTokens: 10
        },
        version: 1,
        isTemplate: false,
        isFavorite: false,
        isArchived: false,
        usage: { count: 0, lastUsed: new Date() },
        createdAt: new Date(),
        updatedAt: new Date(),
        createdBy: 'user'
      };

      const id = await storageManager.prompts.save(prompt);
      
      await storageManager.prompts.update(id, { title: 'Updated Title' });
      
      const updated = await storageManager.prompts.findById(id);
      expect(updated?.title).toBe('Updated Title');
    });

    test('should delete prompt', async () => {
      const prompt: Prompt = {
        title: 'To Delete',
        content: 'Content',
        categoryId: 'general',
        tags: [],
        variables: [],
        metadata: {
          author: 'test',
          createdAt: new Date(),
          updatedAt: new Date(),
          source: 'user',
          language: 'en',
          estimatedTokens: 10
        },
        version: 1,
        isTemplate: false,
        isFavorite: false,
        isArchived: false,
        usage: { count: 0, lastUsed: new Date() },
        createdAt: new Date(),
        updatedAt: new Date(),
        createdBy: 'user'
      };

      const id = await storageManager.prompts.save(prompt);
      
      await storageManager.prompts.delete(id);
      
      const deleted = await storageManager.prompts.findById(id);
      expect(deleted).toBeNull();
    });

    test('should find prompts by category', async () => {
      const prompt1: Prompt = {
        title: 'Prompt 1',
        content: 'Content 1',
        categoryId: 'development',
        tags: [],
        variables: [],
        metadata: {
          author: 'test',
          createdAt: new Date(),
          updatedAt: new Date(),
          source: 'user',
          language: 'en',
          estimatedTokens: 10
        },
        version: 1,
        isTemplate: false,
        isFavorite: false,
        isArchived: false,
        usage: { count: 0, lastUsed: new Date() },
        createdAt: new Date(),
        updatedAt: new Date(),
        createdBy: 'user'
      };

      const prompt2: Prompt = {
        ...prompt1,
        title: 'Prompt 2',
        categoryId: 'writing'
      };

      await storageManager.prompts.save(prompt1);
      await storageManager.prompts.save(prompt2);

      const devPrompts = await storageManager.prompts.findByCategory('development');
      expect(devPrompts).toHaveLength(1);
      expect(devPrompts[0].title).toBe('Prompt 1');
    });

    test('should search prompts by text', async () => {
      const prompt: Prompt = {
        title: 'JavaScript Function',
        content: 'Create a JavaScript function that...',
        categoryId: 'development',
        tags: ['javascript', 'function'],
        variables: [],
        metadata: {
          author: 'test',
          createdAt: new Date(),
          updatedAt: new Date(),
          source: 'user',
          language: 'en',
          estimatedTokens: 10
        },
        version: 1,
        isTemplate: false,
        isFavorite: false,
        isArchived: false,
        usage: { count: 0, lastUsed: new Date() },
        createdAt: new Date(),
        updatedAt: new Date(),
        createdBy: 'user'
      };

      await storageManager.prompts.save(prompt);

      const results = await storageManager.prompts.searchByText('JavaScript');
      expect(results).toHaveLength(1);
      expect(results[0].title).toBe('JavaScript Function');
    });

    test('should toggle favorite status', async () => {
      const prompt: Prompt = {
        title: 'Test Prompt',
        content: 'Content',
        categoryId: 'general',
        tags: [],
        variables: [],
        metadata: {
          author: 'test',
          createdAt: new Date(),
          updatedAt: new Date(),
          source: 'user',
          language: 'en',
          estimatedTokens: 10
        },
        version: 1,
        isTemplate: false,
        isFavorite: false,
        isArchived: false,
        usage: { count: 0, lastUsed: new Date() },
        createdAt: new Date(),
        updatedAt: new Date(),
        createdBy: 'user'
      };

      const id = await storageManager.prompts.save(prompt);
      
      const isFavorite = await storageManager.prompts.toggleFavorite(id);
      expect(isFavorite).toBe(true);

      const updated = await storageManager.prompts.findById(id);
      expect(updated?.isFavorite).toBe(true);
    });

    test('should update usage statistics', async () => {
      const prompt: Prompt = {
        title: 'Test Prompt',
        content: 'Content',
        categoryId: 'general',
        tags: [],
        variables: [],
        metadata: {
          author: 'test',
          createdAt: new Date(),
          updatedAt: new Date(),
          source: 'user',
          language: 'en',
          estimatedTokens: 10
        },
        version: 1,
        isTemplate: false,
        isFavorite: false,
        isArchived: false,
        usage: { count: 0, lastUsed: new Date() },
        createdAt: new Date(),
        updatedAt: new Date(),
        createdBy: 'user'
      };

      const id = await storageManager.prompts.save(prompt);
      
      await storageManager.prompts.updateUsage(id);
      
      const updated = await storageManager.prompts.findById(id);
      expect(updated?.usage.count).toBe(1);
    });
  });

  describe('Category Repository', () => {
    test('should save and retrieve category', async () => {
      const category: Category = {
        name: 'Test Category',
        description: 'Test description',
        color: '#FF0000',
        icon: 'test-icon',
        sortOrder: 0,
        promptCount: 0,
        isSystem: false,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      const id = await storageManager.categories.save(category);
      expect(id).toBeDefined();

      const retrieved = await storageManager.categories.findById(id);
      expect(retrieved).toBeDefined();
      expect(retrieved?.name).toBe(category.name);
    });

    test('should find root categories', async () => {
      const rootCategories = await storageManager.categories.findRootCategories();
      expect(Array.isArray(rootCategories)).toBe(true);
      // Should have default categories from migration
      expect(rootCategories.length).toBeGreaterThan(0);
    });

    test('should find system categories', async () => {
      const systemCategories = await storageManager.categories.findSystemCategories();
      expect(Array.isArray(systemCategories)).toBe(true);
      expect(systemCategories.length).toBeGreaterThan(0);
      expect(systemCategories.every(cat => cat.isSystem)).toBe(true);
    });

    test('should update sort order', async () => {
      const categories = await storageManager.categories.findRootCategories();
      const categoryIds = categories.map(cat => cat.id);
      
      await expect(storageManager.categories.updateSortOrder(categoryIds)).resolves.not.toThrow();
    });
  });

  describe('Settings Repository', () => {
    test('should save and retrieve setting', async () => {
      await storageManager.settings.setValue('test.key', 'test value', 'test');
      
      const value = await storageManager.settings.getValue('test.key');
      expect(value).toBe('test value');
    });

    test('should handle different value types', async () => {
      await storageManager.settings.setValue('string.key', 'string value');
      await storageManager.settings.setValue('number.key', 42);
      await storageManager.settings.setValue('boolean.key', true);
      await storageManager.settings.setValue('object.key', { nested: 'value' });

      expect(await storageManager.settings.getValue('string.key')).toBe('string value');
      expect(await storageManager.settings.getValue('number.key')).toBe(42);
      expect(await storageManager.settings.getValue('boolean.key')).toBe(true);
      expect(await storageManager.settings.getValue('object.key')).toEqual({ nested: 'value' });
    });

    test('should get settings by category', async () => {
      await storageManager.settings.setValue('cat1.key1', 'value1', 'category1');
      await storageManager.settings.setValue('cat1.key2', 'value2', 'category1');
      await storageManager.settings.setValue('cat2.key1', 'value3', 'category2');

      const category1Settings = await storageManager.settings.getByCategory('category1');
      expect(category1Settings).toHaveLength(2);
    });

    test('should get all categories', async () => {
      const categories = await storageManager.settings.getCategories();
      expect(Array.isArray(categories)).toBe(true);
      // Should have default categories from migration
      expect(categories.length).toBeGreaterThan(0);
    });

    test('should bulk update settings', async () => {
      const settings = {
        'bulk.key1': 'value1',
        'bulk.key2': 42,
        'bulk.key3': true
      };

      await storageManager.settings.bulkUpdate(settings, 'bulk');

      expect(await storageManager.settings.getValue('bulk.key1')).toBe('value1');
      expect(await storageManager.settings.getValue('bulk.key2')).toBe(42);
      expect(await storageManager.settings.getValue('bulk.key3')).toBe(true);
    });
  });
});
